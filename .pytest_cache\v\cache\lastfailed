{"api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_operation_creation": true, "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_operation_types": true, "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_insight_creation": true, "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_insight_unique_constraint": true, "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_foreign_key_constraints": true, "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_cascade_delete_behavior": true, "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_memory_set_null_behavior": true, "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_operation_indexes": true, "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_insight_indexes": true, "api/tests/test_evolution_performance.py": true, "api/tests/test_evolution_mcp_tools.py": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_end_to_end_evolution_workflow": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_multi_chunk_evolution_transaction": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_transaction_rollback": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_metrics_aggregation": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_data_consistency": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_concurrent_evolution_operations": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_mcp_evolution_tools_integration": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_performance_requirements": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_success_criteria_validation": true, "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_system_reliability": true}